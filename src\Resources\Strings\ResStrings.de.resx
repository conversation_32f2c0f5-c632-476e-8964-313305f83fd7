<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="BtnSave" xml:space="preserve">
    <value>Speichern</value>
  </data>
  <data name="BtnCancel" xml:space="preserve">
    <value>Stornieren</value>
  </data>
  <data name="BtnClose" xml:space="preserve">
    <value>Schließen</value>
  </data>
  <data name="BtnApply" xml:space="preserve">
    <value>Anwenden</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Ja</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>NEIN</value>
  </data>
  <data name="Options" xml:space="preserve">
    <value>Optionen</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Fehler</value>
  </data>
  <data name="BtnStart" xml:space="preserve">
    <value>Start</value>
  </data>
  <data name="BtnStop" xml:space="preserve">
    <value>Stoppen</value>
  </data>
  <data name="BtnConnect" xml:space="preserve">
    <value>Verbinden</value>
  </data>
  <data name="BtnDisconnect" xml:space="preserve">
    <value>Trennen</value>
  </data>
  <data name="On" xml:space="preserve">
    <value>An</value>
  </data>
  <data name="Off" xml:space="preserve">
    <value>Aus</value>
  </data>
  <data name="VendorTitle" xml:space="preserve">
    <value>Demo</value>
  </data>
  <data name="Dashboard" xml:space="preserve">
    <value>Armaturenbrett</value>
  </data>
  <data name="History" xml:space="preserve">
    <value>Geschichte</value>
  </data>
  <data name="Filtered" xml:space="preserve">
    <value>Gefiltert</value>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>Filter</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>Alle</value>
  </data>
  <data name="Select" xml:space="preserve">
    <value>Wählen</value>
  </data>
  <data name="Order" xml:space="preserve">
    <value>Befehl</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>Benutzer</value>
  </data>
  <data name="EditList" xml:space="preserve">
    <value>Liste bearbeiten</value>
  </data>
  <data name="Users" xml:space="preserve">
    <value>Benutzer</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="Deleting" xml:space="preserve">
    <value>Löschen</value>
  </data>
  <data name="AreYouSure" xml:space="preserve">
    <value>Bist du sicher?</value>
  </data>
  <data name="Europe" xml:space="preserve">
    <value>Europa</value>
  </data>
  <data name="OrderOldFirst" xml:space="preserve">
    <value>Älter zuerst</value>
  </data>
  <data name="OrderNewFirst" xml:space="preserve">
    <value>Neuere zuerst</value>
  </data>
  <data name="Start" xml:space="preserve">
    <value>Start</value>
  </data>
  <data name="End" xml:space="preserve">
    <value>Ende</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>Sprache</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Bearbeiten</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Löschen</value>
  </data>
  <data name="HoursShort" xml:space="preserve">
    <value>H</value>
  </data>
  <data name="BtnReset" xml:space="preserve">
    <value>Zurücksetzen</value>
  </data>
  <data name="BtnCreate" xml:space="preserve">
    <value>Erstellen</value>
  </data>
  <data name="SystemSettings" xml:space="preserve">
    <value>Systemeinstellungen</value>
  </data>
  <data name="AboutApp" xml:space="preserve">
    <value>Um</value>
  </data>
  <data name="BtnQuestion" xml:space="preserve">
    <value>Stelle eine Frage</value>
  </data>
  <data name="ValidationFieldRequired" xml:space="preserve">
    <value>Feld {0} nicht ausgefüllt</value>
  </data>
  <data name="AuthForgotPasswordQuestion" xml:space="preserve">
    <value>Passwort vergessen?</value>
  </data>
  <data name="AuthLogin" xml:space="preserve">
    <value>Anmeldung</value>
  </data>
  <data name="AuthRememberMe" xml:space="preserve">
    <value>Erinnere dich an mich</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Passwort</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>Hinzufügen</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Suchen</value>
  </data>
  <data name="FullName" xml:space="preserve">
    <value>Vollständiger Name</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>Datum</value>
  </data>
  <data name="Manager" xml:space="preserve">
    <value>Manager</value>
  </data>
  <data name="Managers" xml:space="preserve">
    <value>Manager</value>
  </data>
  <data name="Teacher" xml:space="preserve">
    <value>Lehrer</value>
  </data>
  <data name="Teachers" xml:space="preserve">
    <value>Lehrer</value>
  </data>
  <data name="Leads" xml:space="preserve">
    <value>Führt</value>
  </data>
  <data name="Words" xml:space="preserve">
    <value>Wörter</value>
  </data>
  <data name="Word" xml:space="preserve">
    <value>Wort</value>
  </data>
  <data name="Group" xml:space="preserve">
    <value>Gruppe</value>
  </data>
  <data name="Groups" xml:space="preserve">
    <value>Gruppe</value>
  </data>
  <data name="Offices" xml:space="preserve">
    <value>Büros</value>
  </data>
  <data name="Office" xml:space="preserve">
    <value>Büro</value>
  </data>
  <data name="Translation" xml:space="preserve">
    <value>Übersetzung</value>
  </data>
  <data name="Difficulty" xml:space="preserve">
    <value>Schwierigkeit</value>
  </data>
  <data name="Region" xml:space="preserve">
    <value>Region</value>
  </data>
  <data name="Regions" xml:space="preserve">
    <value>Regionen</value>
  </data>
  <data name="Interaction" xml:space="preserve">
    <value>Interaktion</value>
  </data>
  <data name="Shop" xml:space="preserve">
    <value>Geschäft</value>
  </data>
  <data name="Home" xml:space="preserve">
    <value>Heim</value>
  </data>
  <data name="Events" xml:space="preserve">
    <value>Veranstaltungen</value>
  </data>
  <data name="Event" xml:space="preserve">
    <value>Ereignis</value>
  </data>
  <data name="Pushes" xml:space="preserve">
    <value>Drückt</value>
  </data>
  <data name="Push" xml:space="preserve">
    <value>Drücken</value>
  </data>
  <data name="Orders" xml:space="preserve">
    <value>Aufträge</value>
  </data>
  <data name="OrderBy" xml:space="preserve">
    <value>Befehl</value>
  </data>
  <data name="Products" xml:space="preserve">
    <value>Produkte</value>
  </data>
  <data name="Product" xml:space="preserve">
    <value>Produkt</value>
  </data>
  <data name="LogOut" xml:space="preserve">
    <value>Ausloggen</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>Titel</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>Menge</value>
  </data>
  <data name="Price" xml:space="preserve">
    <value>Preis</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="PhoneNumber" xml:space="preserve">
    <value>Telefon</value>
  </data>
  <data name="Birthday" xml:space="preserve">
    <value>Geburtstag</value>
  </data>
  <data name="Balance" xml:space="preserve">
    <value>Gleichgewicht</value>
  </data>
  <data name="LastName" xml:space="preserve">
    <value>Familienname, Nachname</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>Vorname</value>
  </data>
  <data name="PastDue" xml:space="preserve">
    <value>Überfällig</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>Adresse</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>Anmerkungen</value>
  </data>
  <data name="Days" xml:space="preserve">
    <value>Tage</value>
  </data>
  <data name="Time" xml:space="preserve">
    <value>Zeit</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="Messenger" xml:space="preserve">
    <value>Bote</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Beschreibung</value>
  </data>
  <data name="ValidationRequired" xml:space="preserve">
    <value>Pflichtfeld</value>
  </data>
  <data name="Type" xml:space="preserve">
    <value>Typ</value>
  </data>
  <data name="Text" xml:space="preserve">
    <value>Text</value>
  </data>
  <data name="MiddleName" xml:space="preserve">
    <value>Zweiter Vorname</value>
  </data>
  <data name="Success" xml:space="preserve">
    <value>Erfolg!</value>
  </data>
  <data name="Request" xml:space="preserve">
    <value>Anfrage</value>
  </data>
  <data name="Requests" xml:space="preserve">
    <value>Anfragen</value>
  </data>
  <data name="Edited" xml:space="preserve">
    <value>Bearbeitet</value>
  </data>
  <data name="TitleDesk" xml:space="preserve">
    <value>Administrationsmenü</value>
  </data>
  <data name="Camera" xml:space="preserve">
    <value>Kamera</value>
  </data>
  <data name="Cameras" xml:space="preserve">
    <value>Kameras</value>
  </data>
  <data name="ConfirmationNeeded" xml:space="preserve">
    <value>Bestätigung erforderlich</value>
  </data>
  <data name="ConfirmDelete" xml:space="preserve">
    <value>Sind Sie sicher, dass Sie diesen Eintrag löschen möchten?</value>
  </data>
  <data name="ConfirmAction" xml:space="preserve">
    <value>Bitte bestätigen Sie diese Aktion!</value>
  </data>
  <data name="Unset" xml:space="preserve">
    <value>Unscharf</value>
  </data>
  <data name="None" xml:space="preserve">
    <value>Keiner</value>
  </data>
  <data name="Version" xml:space="preserve">
    <value>Ausführung</value>
  </data>
  <data name="Remove" xml:space="preserve">
    <value>Entfernen</value>
  </data>
  <data name="StudyPlanType_Other" xml:space="preserve">
    <value>Andere</value>
  </data>
  <data name="Welcome" xml:space="preserve">
    <value>Willkommen</value>
  </data>
  <data name="RowsPerPage" xml:space="preserve">
    <value>Zeilen pro Seite</value>
  </data>
  <data name="NoRights" xml:space="preserve">
    <value>Unerlaubt</value>
  </data>
  <data name="OrderStatus_Received" xml:space="preserve">
    <value>wird bearbeitet</value>
  </data>
  <data name="OrderStatus_Issued" xml:space="preserve">
    <value>An den Kunden geliefert</value>
  </data>
  <data name="OrderStatus_Sent" xml:space="preserve">
    <value>Ins Büro geschickt</value>
  </data>
  <data name="Amount" xml:space="preserve">
    <value>Menge</value>
  </data>
  <data name="Info" xml:space="preserve">
    <value>Die Info</value>
  </data>
  <data name="ChangeBalance" xml:space="preserve">
    <value>Kontostand ändern</value>
  </data>
  <data name="MyProfile" xml:space="preserve">
    <value>Mein Profil</value>
  </data>
  <data name="ResetPassword" xml:space="preserve">
    <value>Passwort zurücksetzen</value>
  </data>
  <data name="UserName" xml:space="preserve">
    <value>Nutzername</value>
  </data>
  <data name="WrongPassword" xml:space="preserve">
    <value>Benutzername oder Passwort falsch</value>
  </data>
  <data name="PressBACKOnceAgain" xml:space="preserve">
    <value>Drücken Sie zum Beenden noch einmal Zurück.</value>
  </data>
  <data name="BtnShare" xml:space="preserve">
    <value>Aktie</value>
  </data>
  <data name="BtnGoBack" xml:space="preserve">
    <value>Zurück</value>
  </data>
  <data name="StatePaused" xml:space="preserve">
    <value>Brechen</value>
  </data>
  <data name="BtnOk" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="DemoMode" xml:space="preserve">
    <value>Demo</value>
  </data>
  <data name="BtnQuit" xml:space="preserve">
    <value>Aufhören</value>
  </data>
  <data name="BtnContinue" xml:space="preserve">
    <value>Weitermachen</value>
  </data>
  <data name="BtnPlayAgain" xml:space="preserve">
    <value>Wieder spielen</value>
  </data>
  <data name="MessageGameOver" xml:space="preserve">
    <value>Spiel vorbei!\nEndpunktzahl: {0}\nViel Glück beim nächsten Mal!</value>
  </data>
  <data name="StartGame" xml:space="preserve">
    <value>Spiel starten</value>
  </data>
  <data name="MessageLevelComplete" xml:space="preserve">
    <value>Level {0} abgeschlossen!\nPunktzahl: {1}\nMach dich bereit für Level {2}!</value>
  </data>
  <data name="MessageWelcome" xml:space="preserve">
    <value>Willkommen bei Breakout!\nBenutze Gesten, um das Paddle zu bewegen. Zerstöre alle Steine, um zu gewinnen!</value>
  </data>
  <data name="MessageWelcomeDesktop" xml:space="preserve">
    <value>Willkommen bei Breakout!\nBenutze die Maus oder Tastatur, um das Paddle zu bewegen. Zerstöre alle Steine, um zu gewinnen!</value>
  </data>
</root>