﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="HoursShort" xml:space="preserve">
    <value>h</value>
  </data>
  <data name="BtnSave" xml:space="preserve">
    <value>êµ¬í•˜ë‹¤</value>
  </data>
  <data name="BtnCancel" xml:space="preserve">
    <value>ì·¨ì†Œ</value>
  </data>
  <data name="BtnClose" xml:space="preserve">
    <value>ë‹«ë‹¤</value>
  </data>
  <data name="BtnApply" xml:space="preserve">
    <value>ì ìš©í•˜ë‹¤</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>ì˜ˆ</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>ì•„ë‹ˆìš”</value>
  </data>
  <data name="Options" xml:space="preserve">
    <value>ì˜µì…˜</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>ì˜¤ë¥˜</value>
  </data>
  <data name="BtnStart" xml:space="preserve">
    <value>ì‹œìž‘</value>
  </data>
  <data name="BtnStop" xml:space="preserve">
    <value>ë©ˆì¶”ë‹¤</value>
  </data>
  <data name="BtnConnect" xml:space="preserve">
    <value>ì—°ê²°í•˜ë‹¤</value>
  </data>
  <data name="BtnDisconnect" xml:space="preserve">
    <value>ì—°ê²°ì„ ëŠìŠµë‹ˆë‹¤</value>
  </data>
  <data name="On" xml:space="preserve">
    <value>~ì—</value>
  </data>
  <data name="Off" xml:space="preserve">
    <value>ë„ë‹¤</value>
  </data>
  <data name="VendorTitle" xml:space="preserve">
    <value>ë°ëª¨</value>
  </data>
  <data name="Dashboard" xml:space="preserve">
    <value>ê³„ê¸°ë°˜</value>
  </data>
  <data name="History" xml:space="preserve">
    <value>ì—­ì‚¬</value>
  </data>
  <data name="Filtered" xml:space="preserve">
    <value>ê±°ë¥´ëŠ”</value>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>í•„í„°</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>ëª¨ë‘</value>
  </data>
  <data name="Select" xml:space="preserve">
    <value>ì„ íƒí•˜ë‹¤</value>
  </data>
  <data name="Order" xml:space="preserve">
    <value>ì£¼ë¬¸í•˜ë‹¤</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>ì‚¬ìš©ìž</value>
  </data>
  <data name="EditList" xml:space="preserve">
    <value>ëª©ë¡ íŽ¸ì§‘</value>
  </data>
  <data name="Users" xml:space="preserve">
    <value>ì‚¬ìš©ìž</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>ì´ë¦„</value>
  </data>
  <data name="Deleting" xml:space="preserve">
    <value>ì‚­ì œ</value>
  </data>
  <data name="AreYouSure" xml:space="preserve">
    <value>í™•ì‹¤í•©ë‹ˆê¹Œ?</value>
  </data>
  <data name="Europe" xml:space="preserve">
    <value>ìœ ëŸ½</value>
  </data>
  <data name="OrderOldFirst" xml:space="preserve">
    <value>ë” ì˜¤ëž˜ëœ</value>
  </data>
  <data name="OrderNewFirst" xml:space="preserve">
    <value>ìƒˆë¡œìš´ ì²« ë²ˆì§¸</value>
  </data>
  <data name="Start" xml:space="preserve">
    <value>ì‹œìž‘</value>
  </data>
  <data name="End" xml:space="preserve">
    <value>ë</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>ì–¸ì–´</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>íŽ¸ì§‘í•˜ë‹¤</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>ì‚­ì œ</value>
  </data>
  <data name="BtnReset" xml:space="preserve">
    <value>ë‹¤ì‹œ ë†“ê¸°</value>
  </data>
  <data name="BtnCreate" xml:space="preserve">
    <value>ë§Œë“¤ë‹¤</value>
  </data>
  <data name="SystemSettings" xml:space="preserve">
    <value>ì‹œìŠ¤í…œ ì„¤ì •</value>
  </data>
  <data name="AboutApp" xml:space="preserve">
    <value>ì— ëŒ€í•œ</value>
  </data>
  <data name="BtnQuestion" xml:space="preserve">
    <value>ì§ˆë¬¸í•˜ì‹­ì‹œì˜¤</value>
  </data>
  <data name="ValidationFieldRequired" xml:space="preserve">
    <value>{0} í•„ë“œê°€ ì±„ì›Œì§€ì§€ ì•Šì•˜ìŠµë‹ˆë‹¤</value>
  </data>
  <data name="AuthForgotPasswordQuestion" xml:space="preserve">
    <value>ë¹„ë°€ë²ˆí˜¸ë¥¼ ìžŠìœ¼ ì…¨ë‚˜ìš”?</value>
  </data>
  <data name="AuthLogin" xml:space="preserve">
    <value>ë¡œê·¸ì¸</value>
  </data>
  <data name="AuthRememberMe" xml:space="preserve">
    <value>ë‚˜ë¥¼ ê¸°ì–µí•˜ì‹­ì‹œì˜¤</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>ë¹„ë°€ë²ˆí˜¸</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>ì¶”ê°€í•˜ë‹¤</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>ì°¾ë‹¤</value>
  </data>
  <data name="FullName" xml:space="preserve">
    <value>ì „ì²´ ì´ë¦„</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>ë‚ ì§œ</value>
  </data>
  <data name="Manager" xml:space="preserve">
    <value>ê´€ë¦¬ìž</value>
  </data>
  <data name="Managers" xml:space="preserve">
    <value>ê´€ë¦¬ìž</value>
  </data>
  <data name="Teacher" xml:space="preserve">
    <value>ì„ ìƒë‹˜</value>
  </data>
  <data name="Teachers" xml:space="preserve">
    <value>êµì‚¬</value>
  </data>
  <data name="Leads" xml:space="preserve">
    <value>ë¦¬ë“œ</value>
  </data>
  <data name="Words" xml:space="preserve">
    <value>ë‹¨ì–´</value>
  </data>
  <data name="Word" xml:space="preserve">
    <value>ë‹¨ì–´</value>
  </data>
  <data name="Group" xml:space="preserve">
    <value>ê·¸ë£¹</value>
  </data>
  <data name="Groups" xml:space="preserve">
    <value>ê·¸ë£¹</value>
  </data>
  <data name="Offices" xml:space="preserve">
    <value>ë¶€ì—Œ</value>
  </data>
  <data name="Office" xml:space="preserve">
    <value>ì‚¬ë¬´ì‹¤</value>
  </data>
  <data name="Translation" xml:space="preserve">
    <value>ë²ˆì—­</value>
  </data>
  <data name="Difficulty" xml:space="preserve">
    <value>ì–´ë ¤ì›€</value>
  </data>
  <data name="Region" xml:space="preserve">
    <value>ì§€ì—­</value>
  </data>
  <data name="Regions" xml:space="preserve">
    <value>ì§€ì—­</value>
  </data>
  <data name="Interaction" xml:space="preserve">
    <value>ìƒí˜¸ ìž‘ìš©</value>
  </data>
  <data name="Shop" xml:space="preserve">
    <value>ê°€ê²Œ</value>
  </data>
  <data name="Home" xml:space="preserve">
    <value>ì§‘</value>
  </data>
  <data name="Events" xml:space="preserve">
    <value>ì´ë²¤íŠ¸</value>
  </data>
  <data name="Event" xml:space="preserve">
    <value>ì´ë²¤íŠ¸</value>
  </data>
  <data name="Pushes" xml:space="preserve">
    <value>í‘¸ì‹œ</value>
  </data>
  <data name="Push" xml:space="preserve">
    <value>í‘¸ì‹œ</value>
  </data>
  <data name="Orders" xml:space="preserve">
    <value>ëª…ë ¹</value>
  </data>
  <data name="OrderBy" xml:space="preserve">
    <value>ì£¼ë¬¸í•˜ë‹¤</value>
  </data>
  <data name="Products" xml:space="preserve">
    <value>ì œí’ˆ</value>
  </data>
  <data name="Product" xml:space="preserve">
    <value>ì œí’ˆ</value>
  </data>
  <data name="LogOut" xml:space="preserve">
    <value>ë¡œê·¸ ì•„ì›ƒí•˜ì‹­ì‹œì˜¤</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>ì œëª©</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>ìˆ˜ëŸ‰</value>
  </data>
  <data name="Price" xml:space="preserve">
    <value>ê°€ê²©</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>ìƒíƒœ</value>
  </data>
  <data name="PhoneNumber" xml:space="preserve">
    <value>í•¸ë“œí°</value>
  </data>
  <data name="Birthday" xml:space="preserve">
    <value>ìƒì¼</value>
  </data>
  <data name="Balance" xml:space="preserve">
    <value>ê· í˜•</value>
  </data>
  <data name="LastName" xml:space="preserve">
    <value>ì„±</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>ì´ë¦„</value>
  </data>
  <data name="PastDue" xml:space="preserve">
    <value>ê³¼ê±°</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>ì£¼ì†Œ</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>ë©”ëª¨</value>
  </data>
  <data name="Days" xml:space="preserve">
    <value>ë‚ </value>
  </data>
  <data name="Time" xml:space="preserve">
    <value>ì‹œê°„</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>ì´ë©”ì¼</value>
  </data>
  <data name="Messenger" xml:space="preserve">
    <value>ì „ë ¹</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>ì„¤ëª…</value>
  </data>
  <data name="ValidationRequired" xml:space="preserve">
    <value>í•„ìˆ˜ í•„ë“œ</value>
  </data>
  <data name="Type" xml:space="preserve">
    <value>ìœ í˜•</value>
  </data>
  <data name="Text" xml:space="preserve">
    <value>í…ìŠ¤íŠ¸</value>
  </data>
  <data name="MiddleName" xml:space="preserve">
    <value>ì¤‘ê°„ ì´ë¦„</value>
  </data>
  <data name="Success" xml:space="preserve">
    <value>ì„±ê³µ!</value>
  </data>
  <data name="Request" xml:space="preserve">
    <value>ìš”êµ¬</value>
  </data>
  <data name="Requests" xml:space="preserve">
    <value>ìš”ì²­</value>
  </data>
  <data name="Edited" xml:space="preserve">
    <value>íŽ¸ì§‘</value>
  </data>
  <data name="TitleDesk" xml:space="preserve">
    <value>ê´€ë¦¬ìž íŒ¨ë„</value>
  </data>
  <data name="Camera" xml:space="preserve">
    <value>ì¹´ë©”ë¼</value>
  </data>
  <data name="Cameras" xml:space="preserve">
    <value>ì¹´ë©”ë¼</value>
  </data>
  <data name="ConfirmationNeeded" xml:space="preserve">
    <value>í™•ì¸ì´ í•„ìš”í•©ë‹ˆë‹¤</value>
  </data>
  <data name="ConfirmDelete" xml:space="preserve">
    <value>ì´ í•­ëª©ì„ ì‚­ì œ í•˜ì‹œê² ìŠµë‹ˆê¹Œ?</value>
  </data>
  <data name="ConfirmAction" xml:space="preserve">
    <value>ì´ ì¡°ì¹˜ë¥¼ í™•ì¸í•˜ì‹­ì‹œì˜¤!</value>
  </data>
  <data name="Unset" xml:space="preserve">
    <value>ì„¸íŠ¸ê°€ ì—†ë‹¤</value>
  </data>
  <data name="None" xml:space="preserve">
    <value>ì—†ìŒ</value>
  </data>
  <data name="Version" xml:space="preserve">
    <value>ë²„ì „</value>
  </data>
  <data name="Remove" xml:space="preserve">
    <value>ì œê±°í•˜ë‹¤</value>
  </data>
  <data name="StudyPlanType_Other" xml:space="preserve">
    <value>ë‹¤ë¥¸</value>
  </data>
  <data name="Welcome" xml:space="preserve">
    <value>í™˜ì˜</value>
  </data>
  <data name="RowsPerPage" xml:space="preserve">
    <value>íŽ˜ì´ì§€ ë‹¹ í–‰</value>
  </data>
  <data name="NoRights" xml:space="preserve">
    <value>ìžŠí˜€ì§€ì§€ ì•Šì•˜ìŠµë‹ˆë‹¤</value>
  </data>
  <data name="OrderStatus_Received" xml:space="preserve">
    <value>ì²˜ë¦¬</value>
  </data>
  <data name="OrderStatus_Issued" xml:space="preserve">
    <value>ê³ ê°ì—ê²Œ ì „ë‹¬</value>
  </data>
  <data name="OrderStatus_Sent" xml:space="preserve">
    <value>ì‚¬ë¬´ì‹¤ë¡œ ë³´ëƒˆìŠµë‹ˆë‹¤</value>
  </data>
  <data name="Amount" xml:space="preserve">
    <value>ì–‘</value>
  </data>
  <data name="Info" xml:space="preserve">
    <value>ì •ë³´</value>
  </data>
  <data name="ChangeBalance" xml:space="preserve">
    <value>ê· í˜•ì„ ë³€ê²½í•˜ì‹­ì‹œì˜¤</value>
  </data>
  <data name="MyProfile" xml:space="preserve">
    <value>ë‚´ í”„ë¡œí•„</value>
  </data>
  <data name="ResetPassword" xml:space="preserve">
    <value>ë¹„ë°€ë²ˆí˜¸ë¥¼ ìž¬ì„¤ì •í•˜ì‹­ì‹œì˜¤</value>
  </data>
  <data name="UserName" xml:space="preserve">
    <value>ì‚¬ìš©ìž ì´ë¦„</value>
  </data>
  <data name="WrongPassword" xml:space="preserve">
    <value>ìž˜ëª»ëœ ì‚¬ìš©ìž ì´ë¦„ ë˜ëŠ” ë¹„ë°€ë²ˆí˜¸</value>
  </data>
  <data name="PressBACKOnceAgain" xml:space="preserve">
    <value>ë‹¤ì‹œ í•œ ë²ˆ ëˆŒëŸ¬ ë‚˜ê°€ì‹­ì‹œì˜¤ ..</value>
  </data>
  <data name="BtnShare" xml:space="preserve">
    <value>ê³µìœ í•˜ë‹¤</value>
  </data>
  <data name="BtnGoBack" xml:space="preserve">
    <value>ëŒì•„ ê°€ë¼</value>
  </data>
  <data name="StatePaused" xml:space="preserve">
    <value>ì¼ì‹œ ì¤‘ì§€</value>
  </data>
  <data name="BtnOk" xml:space="preserve">
    <value>ì¢‹ì•„ìš”</value>
  </data>
  <data name="DemoMode" xml:space="preserve">
    <value>ë°ëª¨</value>
  </data>
  <data name="BtnQuit" xml:space="preserve">
    <value>ê·¸ë§Œë‘ë‹¤</value>
  </data>
  <data name="BtnContinue" xml:space="preserve">
    <value>ê³„ì†í•˜ë‹¤</value>
  </data>
  <data name="BtnPlayAgain" xml:space="preserve">
    <value>ë†€ë‹¤</value>
  </data>
  <data name="MessageGameOver" xml:space="preserve">
    <value>ê²Œìž„ ì˜¤ë²„!
ìµœì¢… ì ìˆ˜: {0}
ë‹¤ìŒë²ˆì—” ë” ìž˜í•˜ì„¸ìš”!</value>
  </data>
  <data name="StartGame" xml:space="preserve">
    <value>ê²Œìž„ ì‹œìž‘</value>
  </data>
  <data name="MessageLevelComplete" xml:space="preserve">
    <value>ë ˆë²¨ {0} ì™„ë£Œ!
ì ìˆ˜: {1}
ë ˆë²¨ {2} ì¤€ë¹„í•˜ì„¸ìš”!</value>
  </data>
  <data name="MessageWelcomeDesktop" xml:space="preserve">
    <value>ë¸Œë ˆì´í¬ì•„ì›ƒì— ì˜¤ì‹  ê²ƒì„ í™˜ì˜í•©ë‹ˆë‹¤!
ë§ˆìš°ìŠ¤ë‚˜ í‚¤ë³´ë“œë¡œ íŒ¨ë“¤ì„ ì›€ì§ì´ì„¸ìš”. ëª¨ë“  ë²½ëŒì„ ê¹¨ëœ¨ë ¤ ìŠ¹ë¦¬í•˜ì„¸ìš”!</value>
  </data>
  <data name="MessageWelcome" xml:space="preserve">
    <value>ë¸Œë ˆì´í¬ì•„ì›ƒì— ì˜¤ì‹  ê²ƒì„ í™˜ì˜í•©ë‹ˆë‹¤!
ì œìŠ¤ì²˜ë¡œ íŒ¨ë“¤ì„ ì›€ì§ì´ì„¸ìš”. ëª¨ë“  ë²½ëŒì„ ê¹¨ëœ¨ë ¤ ìŠ¹ë¦¬í•˜ì„¸ìš”!</value>
  </data>
</root>