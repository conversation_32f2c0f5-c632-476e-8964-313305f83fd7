﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="HoursShort" xml:space="preserve">
    <value>h</value>
  </data>
  <data name="BtnSave" xml:space="preserve">
    <value>èŠ‚çœ</value>
  </data>
  <data name="BtnCancel" xml:space="preserve">
    <value>å–æ¶ˆ</value>
  </data>
  <data name="BtnClose" xml:space="preserve">
    <value>å…³é—­</value>
  </data>
  <data name="BtnApply" xml:space="preserve">
    <value>ç”³è¯·</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>æ˜¯çš„</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>ä¸</value>
  </data>
  <data name="Options" xml:space="preserve">
    <value>é€‰é¡¹</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>é”™è¯¯</value>
  </data>
  <data name="BtnStart" xml:space="preserve">
    <value>å¼€å§‹</value>
  </data>
  <data name="BtnStop" xml:space="preserve">
    <value>åœæ­¢</value>
  </data>
  <data name="BtnConnect" xml:space="preserve">
    <value>è¿žæŽ¥</value>
  </data>
  <data name="BtnDisconnect" xml:space="preserve">
    <value>æ–­å¼€</value>
  </data>
  <data name="On" xml:space="preserve">
    <value>åœ¨</value>
  </data>
  <data name="Off" xml:space="preserve">
    <value>ç¦»å¼€</value>
  </data>
  <data name="VendorTitle" xml:space="preserve">
    <value>æ¼”ç¤º</value>
  </data>
  <data name="Dashboard" xml:space="preserve">
    <value>ä»ªè¡¨æ¿</value>
  </data>
  <data name="History" xml:space="preserve">
    <value>åŽ†å²</value>
  </data>
  <data name="Filtered" xml:space="preserve">
    <value>è¿‡æ»¤</value>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>ç­›é€‰</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>å…¨éƒ¨</value>
  </data>
  <data name="Select" xml:space="preserve">
    <value>é€‰æ‹©</value>
  </data>
  <data name="Order" xml:space="preserve">
    <value>å‘½ä»¤</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>ç”¨æˆ·</value>
  </data>
  <data name="EditList" xml:space="preserve">
    <value>ç¼–è¾‘åˆ—è¡¨</value>
  </data>
  <data name="Users" xml:space="preserve">
    <value>ç”¨æˆ·</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>å§“å</value>
  </data>
  <data name="Deleting" xml:space="preserve">
    <value>åˆ é™¤</value>
  </data>
  <data name="AreYouSure" xml:space="preserve">
    <value>ä½ ç¡®å®šå—ï¼Ÿ</value>
  </data>
  <data name="Europe" xml:space="preserve">
    <value>æ¬§æ´²</value>
  </data>
  <data name="OrderOldFirst" xml:space="preserve">
    <value>é¦–å…ˆ</value>
  </data>
  <data name="OrderNewFirst" xml:space="preserve">
    <value>é¦–å…ˆ</value>
  </data>
  <data name="Start" xml:space="preserve">
    <value>å¼€å§‹</value>
  </data>
  <data name="End" xml:space="preserve">
    <value>ç»“å°¾</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>è¯­è¨€</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>ç¼–è¾‘</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>åˆ é™¤</value>
  </data>
  <data name="BtnReset" xml:space="preserve">
    <value>é‡ç½®</value>
  </data>
  <data name="BtnCreate" xml:space="preserve">
    <value>åˆ›é€ </value>
  </data>
  <data name="SystemSettings" xml:space="preserve">
    <value>ç³»ç»Ÿè®¾ç½®</value>
  </data>
  <data name="AboutApp" xml:space="preserve">
    <value>å…³äºŽ</value>
  </data>
  <data name="BtnQuestion" xml:space="preserve">
    <value>é—®ä¸€ä¸ªé—®é¢˜</value>
  </data>
  <data name="ValidationFieldRequired" xml:space="preserve">
    <value>{0}å­—æ®µæœªå¡«å……</value>
  </data>
  <data name="AuthForgotPasswordQuestion" xml:space="preserve">
    <value>å¿˜è®°å¯†ç ï¼Ÿ</value>
  </data>
  <data name="AuthLogin" xml:space="preserve">
    <value>ç™»å½•</value>
  </data>
  <data name="AuthRememberMe" xml:space="preserve">
    <value>è®°ä½è´¦å·</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>å¯†ç </value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>æ·»åŠ </value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>æœç´¢</value>
  </data>
  <data name="FullName" xml:space="preserve">
    <value>å§“å</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>æ—¥æœŸ</value>
  </data>
  <data name="Manager" xml:space="preserve">
    <value>ç»ç†</value>
  </data>
  <data name="Managers" xml:space="preserve">
    <value>ç»ç†</value>
  </data>
  <data name="Teacher" xml:space="preserve">
    <value>è€å¸ˆ</value>
  </data>
  <data name="Teachers" xml:space="preserve">
    <value>è€å¸ˆ</value>
  </data>
  <data name="Leads" xml:space="preserve">
    <value>é“…</value>
  </data>
  <data name="Words" xml:space="preserve">
    <value>å­—</value>
  </data>
  <data name="Word" xml:space="preserve">
    <value>å•è¯</value>
  </data>
  <data name="Group" xml:space="preserve">
    <value>å›¢ä½“</value>
  </data>
  <data name="Groups" xml:space="preserve">
    <value>å›¢ä½“</value>
  </data>
  <data name="Offices" xml:space="preserve">
    <value>åŠžå…¬å®¤</value>
  </data>
  <data name="Office" xml:space="preserve">
    <value>åŠžå…¬å®¤</value>
  </data>
  <data name="Translation" xml:space="preserve">
    <value>ç¿»è¯‘</value>
  </data>
  <data name="Difficulty" xml:space="preserve">
    <value>å›°éš¾</value>
  </data>
  <data name="Region" xml:space="preserve">
    <value>åœ°åŒº</value>
  </data>
  <data name="Regions" xml:space="preserve">
    <value>åœ°åŒº</value>
  </data>
  <data name="Interaction" xml:space="preserve">
    <value>ç›¸äº’ä½œç”¨</value>
  </data>
  <data name="Shop" xml:space="preserve">
    <value>åº—é“º</value>
  </data>
  <data name="Home" xml:space="preserve">
    <value>å®¶</value>
  </data>
  <data name="Events" xml:space="preserve">
    <value>äº‹ä»¶</value>
  </data>
  <data name="Event" xml:space="preserve">
    <value>äº‹ä»¶</value>
  </data>
  <data name="Pushes" xml:space="preserve">
    <value>æŽ¨åŠ¨</value>
  </data>
  <data name="Push" xml:space="preserve">
    <value>æŽ¨</value>
  </data>
  <data name="Orders" xml:space="preserve">
    <value>è®¢å•</value>
  </data>
  <data name="OrderBy" xml:space="preserve">
    <value>å‘½ä»¤</value>
  </data>
  <data name="Products" xml:space="preserve">
    <value>äº§å“</value>
  </data>
  <data name="Product" xml:space="preserve">
    <value>äº§å“</value>
  </data>
  <data name="LogOut" xml:space="preserve">
    <value>æ³¨é”€</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>æ ‡é¢˜</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>æ•°é‡</value>
  </data>
  <data name="Price" xml:space="preserve">
    <value>ä»·æ ¼</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>åœ°ä½</value>
  </data>
  <data name="PhoneNumber" xml:space="preserve">
    <value>ç”µè¯</value>
  </data>
  <data name="Birthday" xml:space="preserve">
    <value>ç”Ÿæ—¥</value>
  </data>
  <data name="Balance" xml:space="preserve">
    <value>å¹³è¡¡</value>
  </data>
  <data name="LastName" xml:space="preserve">
    <value>å§“</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>å</value>
  </data>
  <data name="PastDue" xml:space="preserve">
    <value>è¿‡åŽ»</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>åœ°å€</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>ç¬”è®°</value>
  </data>
  <data name="Days" xml:space="preserve">
    <value>å¤©</value>
  </data>
  <data name="Time" xml:space="preserve">
    <value>æ—¶é—´</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>ç”µå­é‚®ä»¶</value>
  </data>
  <data name="Messenger" xml:space="preserve">
    <value>ä¿¡ä½¿</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>æè¿°</value>
  </data>
  <data name="ValidationRequired" xml:space="preserve">
    <value>æ‰€éœ€å­—æ®µ</value>
  </data>
  <data name="Type" xml:space="preserve">
    <value>ç±»åž‹</value>
  </data>
  <data name="Text" xml:space="preserve">
    <value>æ–‡æœ¬</value>
  </data>
  <data name="MiddleName" xml:space="preserve">
    <value>ä¸­é—´åå­—</value>
  </data>
  <data name="Success" xml:space="preserve">
    <value>æˆåŠŸï¼</value>
  </data>
  <data name="Request" xml:space="preserve">
    <value>è¦æ±‚</value>
  </data>
  <data name="Requests" xml:space="preserve">
    <value>è¯·æ±‚</value>
  </data>
  <data name="Edited" xml:space="preserve">
    <value>ç¼–è¾‘</value>
  </data>
  <data name="TitleDesk" xml:space="preserve">
    <value>ç®¡ç†é¢æ¿</value>
  </data>
  <data name="Camera" xml:space="preserve">
    <value>ç›¸æœº</value>
  </data>
  <data name="Cameras" xml:space="preserve">
    <value>ç›¸æœº</value>
  </data>
  <data name="ConfirmationNeeded" xml:space="preserve">
    <value>éœ€è¦ç¡®è®¤</value>
  </data>
  <data name="ConfirmDelete" xml:space="preserve">
    <value>æ‚¨ç¡®å®šè¦åˆ é™¤æ­¤æ¡ç›®å—ï¼Ÿ</value>
  </data>
  <data name="ConfirmAction" xml:space="preserve">
    <value>è¯·ç¡®è®¤æ­¤æ“ä½œï¼</value>
  </data>
  <data name="Unset" xml:space="preserve">
    <value>ä¸è®¾ç½®</value>
  </data>
  <data name="None" xml:space="preserve">
    <value>æ²¡æœ‰ä»»ä½•</value>
  </data>
  <data name="Version" xml:space="preserve">
    <value>ç‰ˆæœ¬</value>
  </data>
  <data name="Remove" xml:space="preserve">
    <value>æ¶ˆé™¤</value>
  </data>
  <data name="StudyPlanType_Other" xml:space="preserve">
    <value>å…¶ä»–</value>
  </data>
  <data name="Welcome" xml:space="preserve">
    <value>æ¬¢è¿Ž</value>
  </data>
  <data name="RowsPerPage" xml:space="preserve">
    <value>æ¯é¡µè¡Œ</value>
  </data>
  <data name="NoRights" xml:space="preserve">
    <value>ä¸å…è®¸</value>
  </data>
  <data name="OrderStatus_Received" xml:space="preserve">
    <value>åŠ å·¥</value>
  </data>
  <data name="OrderStatus_Issued" xml:space="preserve">
    <value>äº¤ä»˜ç»™å®¢æˆ·</value>
  </data>
  <data name="OrderStatus_Sent" xml:space="preserve">
    <value>é€å¾€åŠžå…¬å®¤</value>
  </data>
  <data name="Amount" xml:space="preserve">
    <value>æ•°é‡</value>
  </data>
  <data name="Info" xml:space="preserve">
    <value>ä¿¡æ¯</value>
  </data>
  <data name="ChangeBalance" xml:space="preserve">
    <value>æ›´æ”¹å¹³è¡¡</value>
  </data>
  <data name="MyProfile" xml:space="preserve">
    <value>æˆ‘çš„ä¸ªäººèµ„æ–™</value>
  </data>
  <data name="ResetPassword" xml:space="preserve">
    <value>é‡ç½®å¯†ç </value>
  </data>
  <data name="UserName" xml:space="preserve">
    <value>ç”¨æˆ·å</value>
  </data>
  <data name="WrongPassword" xml:space="preserve">
    <value>é”™è¯¯çš„ç”¨æˆ·åæˆ–å¯†ç </value>
  </data>
  <data name="PressBACKOnceAgain" xml:space="preserve">
    <value>å†æ¬¡å‘åŽé€€å‡ºã€‚</value>
  </data>
  <data name="BtnShare" xml:space="preserve">
    <value>åˆ†äº«</value>
  </data>
  <data name="BtnGoBack" xml:space="preserve">
    <value>å›žåŽ»</value>
  </data>
  <data name="StatePaused" xml:space="preserve">
    <value>æš‚åœ</value>
  </data>
  <data name="BtnOk" xml:space="preserve">
    <value>å¥½çš„</value>
  </data>
  <data name="DemoMode" xml:space="preserve">
    <value>ç¤ºèŒƒ</value>
  </data>
  <data name="BtnQuit" xml:space="preserve">
    <value>è¾žèŒ</value>
  </data>
  <data name="BtnContinue" xml:space="preserve">
    <value>ç»§ç»­</value>
  </data>
  <data name="BtnPlayAgain" xml:space="preserve">
    <value>çŽ©</value>
  </data>
  <data name="MessageGameOver" xml:space="preserve">
    <value>æ¸¸æˆç»“æŸï¼
æœ€ç»ˆå¾—åˆ†ï¼š{0}
ä¸‹æ¬¡åŠ æ²¹ï¼</value>
  </data>
  <data name="StartGame" xml:space="preserve">
    <value>å¼€å§‹æ¸¸æˆ</value>
  </data>
  <data name="MessageLevelComplete" xml:space="preserve">
    <value>ç¬¬{0}å…³å®Œæˆï¼
å¾—åˆ†ï¼š{1}
å‡†å¤‡ç¬¬{2}å…³ï¼</value>
  </data>
  <data name="MessageWelcomeDesktop" xml:space="preserve">
    <value>æ¬¢è¿Žæ¥åˆ°æ‰“ç –å—æ¸¸æˆï¼
ä½¿ç”¨é¼ æ ‡æˆ–é”®ç›˜æ¥ç§»åŠ¨æŒ¡æ¿ã€‚æ‰“ç ´æ‰€æœ‰ç –å—å³å¯èŽ·èƒœï¼</value>
  </data>
  <data name="MessageWelcome" xml:space="preserve">
    <value>æ¬¢è¿Žæ¥åˆ°æ‰“ç –å—æ¸¸æˆï¼
ä½¿ç”¨æ‰‹åŠ¿æ¥ç§»åŠ¨æŒ¡æ¿ã€‚æ‰“ç ´æ‰€æœ‰ç –å—å³å¯èŽ·èƒœï¼</value>
  </data>
</root>