﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="HoursShort" xml:space="preserve">
    <value>h</value>
  </data>
  <data name="BtnSave" xml:space="preserve">
    <value>ä¿å­˜</value>
  </data>
  <data name="BtnCancel" xml:space="preserve">
    <value>ã‚­ãƒ£ãƒ³ã‚»ãƒ«</value>
  </data>
  <data name="BtnClose" xml:space="preserve">
    <value>è¿‘ã„</value>
  </data>
  <data name="BtnApply" xml:space="preserve">
    <value>é©ç”¨ã™ã‚‹</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>ã¯ã„</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>ã„ã„ãˆ</value>
  </data>
  <data name="Options" xml:space="preserve">
    <value>ã‚ªãƒ—ã‚·ãƒ§ãƒ³</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>ã‚¨ãƒ©ãƒ¼</value>
  </data>
  <data name="BtnStart" xml:space="preserve">
    <value>å§‹ã‚ã‚‹</value>
  </data>
  <data name="BtnStop" xml:space="preserve">
    <value>åœæ­¢</value>
  </data>
  <data name="BtnConnect" xml:space="preserve">
    <value>æŽ¥ç¶šã™ã‚‹</value>
  </data>
  <data name="BtnDisconnect" xml:space="preserve">
    <value>åˆ‡æ–­ã—ã¾ã™</value>
  </data>
  <data name="On" xml:space="preserve">
    <value>ã®ä¸Š</value>
  </data>
  <data name="Off" xml:space="preserve">
    <value>ã‚ªãƒ•</value>
  </data>
  <data name="VendorTitle" xml:space="preserve">
    <value>ãƒ‡ãƒ¢</value>
  </data>
  <data name="Dashboard" xml:space="preserve">
    <value>ãƒ€ãƒƒã‚·ãƒ¥ãƒœãƒ¼ãƒ‰</value>
  </data>
  <data name="History" xml:space="preserve">
    <value>æ­´å²</value>
  </data>
  <data name="Filtered" xml:space="preserve">
    <value>ãƒ•ã‚£ãƒ«ã‚¿ãƒªãƒ³ã‚°</value>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>ãƒ•ã‚£ãƒ«ã‚¿ãƒ¼</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>å…¨ã¦</value>
  </data>
  <data name="Select" xml:space="preserve">
    <value>é¸æŠžã—ã¾ã™</value>
  </data>
  <data name="Order" xml:space="preserve">
    <value>æ³¨æ–‡</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>ãƒ¦ãƒ¼ã‚¶ãƒ¼</value>
  </data>
  <data name="EditList" xml:space="preserve">
    <value>ç·¨é›†ãƒªã‚¹ãƒˆ</value>
  </data>
  <data name="Users" xml:space="preserve">
    <value>ãƒ¦ãƒ¼ã‚¶ãƒ¼</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>åå‰</value>
  </data>
  <data name="Deleting" xml:space="preserve">
    <value>æ¶ˆåŽ»</value>
  </data>
  <data name="AreYouSure" xml:space="preserve">
    <value>æœ¬æ°—ã§ã™ã‹ï¼Ÿ</value>
  </data>
  <data name="Europe" xml:space="preserve">
    <value>ãƒ¨ãƒ¼ãƒ­ãƒƒãƒ‘</value>
  </data>
  <data name="OrderOldFirst" xml:space="preserve">
    <value>æœ€åˆã«å¹´ä¸Š</value>
  </data>
  <data name="OrderNewFirst" xml:space="preserve">
    <value>æ–°ã—ã„æœ€åˆ</value>
  </data>
  <data name="Start" xml:space="preserve">
    <value>å§‹ã‚ã‚‹</value>
  </data>
  <data name="End" xml:space="preserve">
    <value>çµ‚ã‚ã‚Š</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>è¨€èªž</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>ç·¨é›†</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>æ¶ˆåŽ»</value>
  </data>
  <data name="BtnReset" xml:space="preserve">
    <value>ãƒªã‚»ãƒƒãƒˆ</value>
  </data>
  <data name="BtnCreate" xml:space="preserve">
    <value>ä½œæˆã™ã‚‹</value>
  </data>
  <data name="SystemSettings" xml:space="preserve">
    <value>ã‚·ã‚¹ãƒ†ãƒ è¨­å®š</value>
  </data>
  <data name="AboutApp" xml:space="preserve">
    <value>ã«ã¤ã„ã¦</value>
  </data>
  <data name="BtnQuestion" xml:space="preserve">
    <value>è³ªå•ã—ã¦ãã ã•ã„</value>
  </data>
  <data name="ValidationFieldRequired" xml:space="preserve">
    <value>{0}ãƒ•ã‚£ãƒ¼ãƒ«ãƒ‰ãŒæº€ãŸã•ã‚Œã¦ã„ã¾ã›ã‚“</value>
  </data>
  <data name="AuthForgotPasswordQuestion" xml:space="preserve">
    <value>ãƒ‘ã‚¹ãƒ¯ãƒ¼ãƒ‰ã‚’ãŠå¿˜ã‚Œã§ã™ã‹ï¼Ÿ</value>
  </data>
  <data name="AuthLogin" xml:space="preserve">
    <value>ãƒ­ã‚°ã‚¤ãƒ³</value>
  </data>
  <data name="AuthRememberMe" xml:space="preserve">
    <value>ç§ã‚’è¦šãˆã¦ã¾ã™ã‹</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>ãƒ‘ã‚¹ãƒ¯ãƒ¼ãƒ‰</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>è¿½åŠ </value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>æ¤œç´¢</value>
  </data>
  <data name="FullName" xml:space="preserve">
    <value>ãƒ•ãƒ«ãƒãƒ¼ãƒ </value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>æ—¥ä»˜</value>
  </data>
  <data name="Manager" xml:space="preserve">
    <value>ãƒžãƒãƒ¼ã‚¸ãƒ£ãƒ¼</value>
  </data>
  <data name="Managers" xml:space="preserve">
    <value>ãƒžãƒãƒ¼ã‚¸ãƒ£ãƒ¼</value>
  </data>
  <data name="Teacher" xml:space="preserve">
    <value>æ•™å¸«</value>
  </data>
  <data name="Teachers" xml:space="preserve">
    <value>æ•™å¸«</value>
  </data>
  <data name="Leads" xml:space="preserve">
    <value>ãƒªãƒ¼ãƒ‰</value>
  </data>
  <data name="Words" xml:space="preserve">
    <value>è¨€è‘‰</value>
  </data>
  <data name="Word" xml:space="preserve">
    <value>è¨€è‘‰</value>
  </data>
  <data name="Group" xml:space="preserve">
    <value>ã‚°ãƒ«ãƒ¼ãƒ—</value>
  </data>
  <data name="Groups" xml:space="preserve">
    <value>ã‚°ãƒ«ãƒ¼ãƒ—</value>
  </data>
  <data name="Offices" xml:space="preserve">
    <value>ã‚ªãƒ•ã‚£ã‚¹</value>
  </data>
  <data name="Office" xml:space="preserve">
    <value>ã‚ªãƒ•ã‚£ã‚¹</value>
  </data>
  <data name="Translation" xml:space="preserve">
    <value>ç¿»è¨³</value>
  </data>
  <data name="Difficulty" xml:space="preserve">
    <value>å›°é›£</value>
  </data>
  <data name="Region" xml:space="preserve">
    <value>åœ°åŸŸ</value>
  </data>
  <data name="Regions" xml:space="preserve">
    <value>åœ°åŸŸ</value>
  </data>
  <data name="Interaction" xml:space="preserve">
    <value>äº¤æµ</value>
  </data>
  <data name="Shop" xml:space="preserve">
    <value>åº—</value>
  </data>
  <data name="Home" xml:space="preserve">
    <value>å®¶</value>
  </data>
  <data name="Events" xml:space="preserve">
    <value>ã‚¤ãƒ™ãƒ³ãƒˆ</value>
  </data>
  <data name="Event" xml:space="preserve">
    <value>ã‚¤ãƒ™ãƒ³ãƒˆ</value>
  </data>
  <data name="Pushes" xml:space="preserve">
    <value>ãƒ—ãƒƒã‚·ãƒ¥</value>
  </data>
  <data name="Push" xml:space="preserve">
    <value>æŠ¼ã™</value>
  </data>
  <data name="Orders" xml:space="preserve">
    <value>æ³¨æ–‡</value>
  </data>
  <data name="OrderBy" xml:space="preserve">
    <value>æ³¨æ–‡</value>
  </data>
  <data name="Products" xml:space="preserve">
    <value>è£½å“</value>
  </data>
  <data name="Product" xml:space="preserve">
    <value>è£½å“</value>
  </data>
  <data name="LogOut" xml:space="preserve">
    <value>ãƒ­ã‚°ã‚¢ã‚¦ãƒˆ</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>ã‚¿ã‚¤ãƒˆãƒ«</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>é‡</value>
  </data>
  <data name="Price" xml:space="preserve">
    <value>ä¾¡æ ¼</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>çŠ¶æ…‹</value>
  </data>
  <data name="PhoneNumber" xml:space="preserve">
    <value>é›»è©±</value>
  </data>
  <data name="Birthday" xml:space="preserve">
    <value>èª•ç”Ÿæ—¥</value>
  </data>
  <data name="Balance" xml:space="preserve">
    <value>ãƒãƒ©ãƒ³ã‚¹</value>
  </data>
  <data name="LastName" xml:space="preserve">
    <value>è‹—å­—</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>ãƒ•ã‚¡ãƒ¼ã‚¹ãƒˆãƒãƒ¼ãƒ </value>
  </data>
  <data name="PastDue" xml:space="preserve">
    <value>éŽåŽ»ã®æœŸé™</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>ä½æ‰€</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>ãƒ¡ãƒ¢</value>
  </data>
  <data name="Days" xml:space="preserve">
    <value>æ—¥</value>
  </data>
  <data name="Time" xml:space="preserve">
    <value>æ™‚é–“</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>é›»å­ãƒ¡ãƒ¼ãƒ«</value>
  </data>
  <data name="Messenger" xml:space="preserve">
    <value>ãƒ¡ãƒƒã‚»ãƒ³ã‚¸ãƒ£ãƒ¼</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>èª¬æ˜Ž</value>
  </data>
  <data name="ValidationRequired" xml:space="preserve">
    <value>å¿…è¦ãªãƒ•ã‚£ãƒ¼ãƒ«ãƒ‰</value>
  </data>
  <data name="Type" xml:space="preserve">
    <value>ã‚¿ã‚¤ãƒ—</value>
  </data>
  <data name="Text" xml:space="preserve">
    <value>æ–‡ç« </value>
  </data>
  <data name="MiddleName" xml:space="preserve">
    <value>ãƒŸãƒ‰ãƒ«ãƒãƒ¼ãƒ </value>
  </data>
  <data name="Success" xml:space="preserve">
    <value>æˆåŠŸï¼</value>
  </data>
  <data name="Request" xml:space="preserve">
    <value>ãƒªã‚¯ã‚¨ã‚¹ãƒˆ</value>
  </data>
  <data name="Requests" xml:space="preserve">
    <value>ãƒªã‚¯ã‚¨ã‚¹ãƒˆ</value>
  </data>
  <data name="Edited" xml:space="preserve">
    <value>ç·¨é›†</value>
  </data>
  <data name="TitleDesk" xml:space="preserve">
    <value>ç®¡ç†ãƒ‘ãƒãƒ«</value>
  </data>
  <data name="Camera" xml:space="preserve">
    <value>ã‚«ãƒ¡ãƒ©</value>
  </data>
  <data name="Cameras" xml:space="preserve">
    <value>ã‚«ãƒ¡ãƒ©</value>
  </data>
  <data name="ConfirmationNeeded" xml:space="preserve">
    <value>ç¢ºèªãŒå¿…è¦ã§ã™</value>
  </data>
  <data name="ConfirmDelete" xml:space="preserve">
    <value>ã“ã®ã‚¨ãƒ³ãƒˆãƒªã‚’å‰Šé™¤ã—ãŸã„ã§ã™ã‹ï¼Ÿ</value>
  </data>
  <data name="ConfirmAction" xml:space="preserve">
    <value>ã“ã®ã‚¢ã‚¯ã‚·ãƒ§ãƒ³ã‚’ç¢ºèªã—ã¦ãã ã•ã„ï¼</value>
  </data>
  <data name="Unset" xml:space="preserve">
    <value>è§£ãæ”¾ã¤</value>
  </data>
  <data name="None" xml:space="preserve">
    <value>ãªã—</value>
  </data>
  <data name="Version" xml:space="preserve">
    <value>ãƒãƒ¼ã‚¸ãƒ§ãƒ³</value>
  </data>
  <data name="Remove" xml:space="preserve">
    <value>å–ã‚Šé™¤ã</value>
  </data>
  <data name="StudyPlanType_Other" xml:space="preserve">
    <value>ä»–ã®</value>
  </data>
  <data name="Welcome" xml:space="preserve">
    <value>ã„ã‚‰ã£ã—ã‚ƒã„ã¾ã›</value>
  </data>
  <data name="RowsPerPage" xml:space="preserve">
    <value>1ãƒšãƒ¼ã‚¸ã‚ãŸã‚Šã®è¡Œ</value>
  </data>
  <data name="NoRights" xml:space="preserve">
    <value>è¨±ã•ã‚Œãªã„</value>
  </data>
  <data name="OrderStatus_Received" xml:space="preserve">
    <value>å‡¦ç†</value>
  </data>
  <data name="OrderStatus_Issued" xml:space="preserve">
    <value>ã‚¯ãƒ©ã‚¤ã‚¢ãƒ³ãƒˆã«é…ä¿¡ã•ã‚Œã¾ã™</value>
  </data>
  <data name="OrderStatus_Sent" xml:space="preserve">
    <value>ã‚ªãƒ•ã‚£ã‚¹ã«é€ã‚‰ã‚Œã¾ã—ãŸ</value>
  </data>
  <data name="Amount" xml:space="preserve">
    <value>é¡</value>
  </data>
  <data name="Info" xml:space="preserve">
    <value>æƒ…å ±</value>
  </data>
  <data name="ChangeBalance" xml:space="preserve">
    <value>ãƒãƒ©ãƒ³ã‚¹ã‚’å¤‰æ›´ã—ã¾ã™</value>
  </data>
  <data name="MyProfile" xml:space="preserve">
    <value>ç§ã®ãƒ—ãƒ­ãƒ•ã‚£ãƒ¼ãƒ«</value>
  </data>
  <data name="ResetPassword" xml:space="preserve">
    <value>ãƒ‘ã‚¹ãƒ¯ãƒ¼ãƒ‰ã‚’ãƒªã‚»ãƒƒãƒˆã—ã¾ã™</value>
  </data>
  <data name="UserName" xml:space="preserve">
    <value>ãƒ¦ãƒ¼ã‚¶ãƒ¼å</value>
  </data>
  <data name="WrongPassword" xml:space="preserve">
    <value>é–“é•ã£ãŸãƒ¦ãƒ¼ã‚¶ãƒ¼åã¾ãŸã¯ãƒ‘ã‚¹ãƒ¯ãƒ¼ãƒ‰</value>
  </data>
  <data name="PressBACKOnceAgain" xml:space="preserve">
    <value>ã‚‚ã†ä¸€åº¦å†ã³æŠ¼ã—ã¦çµ‚äº†ã—ã¾ã™ã€‚</value>
  </data>
  <data name="BtnShare" xml:space="preserve">
    <value>å…±æœ‰</value>
  </data>
  <data name="BtnGoBack" xml:space="preserve">
    <value>æˆ»ã£ã¦è¡Œãã¾ã™</value>
  </data>
  <data name="StatePaused" xml:space="preserve">
    <value>ä¸€æ™‚åœæ­¢</value>
  </data>
  <data name="BtnOk" xml:space="preserve">
    <value>ã‚ã‹ã‚Šã¾ã—ãŸ</value>
  </data>
  <data name="DemoMode" xml:space="preserve">
    <value>ãƒ‡ãƒ¢ãƒ³ã‚¹ãƒˆãƒ¬ãƒ¼ã‚·ãƒ§ãƒ³</value>
  </data>
  <data name="BtnQuit" xml:space="preserve">
    <value>ã‚„ã‚ã‚‹</value>
  </data>
  <data name="BtnContinue" xml:space="preserve">
    <value>ç¶šã</value>
  </data>
  <data name="BtnPlayAgain" xml:space="preserve">
    <value>éŠã¶</value>
  </data>
  <data name="MessageGameOver" xml:space="preserve">
    <value>ã‚²ãƒ¼ãƒ ã‚ªãƒ¼ãƒãƒ¼ï¼
æœ€çµ‚ã‚¹ã‚³ã‚¢: {0}
æ¬¡å›žé ‘å¼µã£ã¦ï¼</value>
  </data>
  <data name="StartGame" xml:space="preserve">
    <value>ã‚²ãƒ¼ãƒ é–‹å§‹</value>
  </data>
  <data name="MessageLevelComplete" xml:space="preserve">
    <value>ãƒ¬ãƒ™ãƒ«{0}ã‚¯ãƒªã‚¢ï¼
ã‚¹ã‚³ã‚¢: {1}
ãƒ¬ãƒ™ãƒ«{2}ã®æº–å‚™ã‚’ã—ã‚ˆã†ï¼</value>
  </data>
  <data name="MessageWelcomeDesktop" xml:space="preserve">
    <value>ãƒ–ãƒ¬ã‚¤ã‚¯ã‚¢ã‚¦ãƒˆã¸ã‚ˆã†ã“ãï¼
ãƒžã‚¦ã‚¹ã¾ãŸã¯ã‚­ãƒ¼ãƒœãƒ¼ãƒ‰ã§ãƒ‘ãƒ‰ãƒ«ã‚’å‹•ã‹ã—ã¾ã™ã€‚ã™ã¹ã¦ã®ãƒ–ãƒ­ãƒƒã‚¯ã‚’å£Šã—ã¦å‹åˆ©ã—ã‚ˆã†ï¼</value>
  </data>
  <data name="MessageWelcome" xml:space="preserve">
    <value>ãƒ–ãƒ¬ã‚¤ã‚¯ã‚¢ã‚¦ãƒˆã¸ã‚ˆã†ã“ãï¼
ã‚¸ã‚§ã‚¹ãƒãƒ£ãƒ¼ã§ãƒ‘ãƒ‰ãƒ«ã‚’å‹•ã‹ã—ã¾ã™ã€‚ã™ã¹ã¦ã®ãƒ–ãƒ­ãƒƒã‚¯ã‚’å£Šã—ã¦å‹åˆ©ã—ã‚ˆã†ï¼</value>
  </data>
</root>