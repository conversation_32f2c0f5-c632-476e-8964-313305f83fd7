﻿using BreakoutGame.Game;
using BreakoutGame.Resources.Strings;
using DrawnUi.Controls;
using DrawnUi.Views;
using PreviewFramework;
using System.Globalization;
using Breakout;

namespace BreakoutGame
{
    public class MainPage : BasePageReloadable
    {

        public MainPage()
        {
            ApplyLanguage("ru");

            ApplySelectedLanguage();
        }

        public void ApplySelectedLanguage()
        {
            if (Preferences.Get("FirstStart", true))
            {
                if (!MauiProgram.Languages.Contains(Preferences.Get("DeviceLang", "")))
                {
                    var fallback = MauiProgram.Languages.First();
                    ApplyLanguage(fallback); // will set SelectedLang
                }
            }

            ApplyLanguage(Preferences.Get("SelectedLang", ""));
        }

        public void ApplyLanguage(string lang)
        {
            ResStrings.Culture = CultureInfo.CreateSpecificCulture(lang);
            Thread.CurrentThread.CurrentCulture = ResStrings.Culture;
            Thread.CurrentThread.CurrentUICulture = ResStrings.Culture;
            Preferences.Set("DeviceLang", lang);
            Preferences.Set("SelectedLang", lang);
        }

        public static void RestartWithLanguage(string lang)
        {
            if (App.Current.MainPage is MainPage mainPage)
            {
                mainPage.ApplyLanguage(lang);
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    App.Current.MainPage = new MainPage();
                });
            }
        }

        Canvas Canvas;

        public override void Build()
        {
            Canvas?.Dispose();

            Canvas = new Canvas()
            {
                HorizontalOptions = LayoutOptions.Fill,
                VerticalOptions = LayoutOptions.Fill,
                Gestures = GesturesMode.Lock,
                RenderingMode = RenderingModeType.Accelerated,
                BackgroundColor = Colors.Black,

                Content = new SkiaLayer()
                {
                    Children =
                    {
                        new SkiaViewSwitcher()
                        {
                            HorizontalOptions = LayoutOptions.Fill,
                            VerticalOptions = LayoutOptions.Fill,
                            SelectedIndex = 0,
                            Children =
                            {
                                new Game.BreakoutGame(),
                            }
                        }.Assign(out ViewsContainer),

                        new SkiaLabelFps()
                        {
                            Margin = new(0, 0, 4, 24),
                            VerticalOptions = LayoutOptions.End,
                            HorizontalOptions = LayoutOptions.End,
                            Rotation = -45,
                            FontSize = 11,
                            BackgroundColor = Colors.DarkRed,
                            TextColor = Colors.White,
                            ZIndex = 110,
                        }
                    }
                }.Fill()
            };

            this.Content = Canvas;
        }

#if PREVIEWS
        [Preview]
        public static void Welcome() => ApplyPreviewState(new PreviewAppState() { GameState = GameState.Ready });

        [Preview]
        public static void Playing() => ApplyPreviewState(new PreviewAppState() { GameState = GameState.Playing });

        [Preview]
        public static void Paused() => ApplyPreviewState(new PreviewAppState() { GameState = GameState.Paused });

        [Preview]
        public static void Ended() => ApplyPreviewState(new PreviewAppState() { GameState = GameState.Ended });

        [Preview]
        public static void LevelComplete() =>
            ApplyPreviewState(new PreviewAppState() { GameState = GameState.LevelComplete });

        [Preview]
        public static void DemoPlay() => ApplyPreviewState(new PreviewAppState() { GameState = GameState.DemoPlay });

        [Preview]
        public static void Level2() => ApplyPreviewState(PreviewAppState.BeginningOfLevel(2));

        [Preview]
        public static void Level3() => ApplyPreviewState(PreviewAppState.BeginningOfLevel(3));

        [Preview]
        public static void Level4() => ApplyPreviewState(PreviewAppState.BeginningOfLevel(4));

        [Preview]
        public static void Level5() => ApplyPreviewState(PreviewAppState.BeginningOfLevel(5));

        private static void ApplyPreviewState(PreviewAppState previewAppState)
        {
            var breakoutGame = Game.BreakoutGame.Instance ??
                               throw new InvalidOperationException("BreakoutGame isn't initialized");

            breakoutGame.ApplyPreviewState(previewAppState);
        }

        [Preview]
        public static void Paddle() => Preview(new PaddleSprite());

        public static SkiaViewSwitcher? ViewsContainer;

        public static SkiaLayout CreatePreviewWrapper(SkiaControl control)
        {
            return new SkiaStack()
            {
                Spacing = 0,
                BackgroundColor = Colors.DarkGrey,
                VerticalOptions = LayoutOptions.Fill,
                Children =
                {
                    new SkiaLayer()
                    {
                        HeightRequest = 40,
                        BackgroundColor = Colors.Black,
                        Children =
                        {
                            new SkiaRichLabel($"← {ResStrings.BtnGoBack}")
                                {
                                    VerticalOptions = LayoutOptions.Center,
                                    Padding = new(16, 0),
                                    UseCache = SkiaCacheType.Operations
                                }
                                .OnTapped(me =>
                                {
                                    _ = ViewsContainer.PopPage();
                                })
                        }
                    },
                    control
                }
            };
        }

        private static void Preview(SkiaControl control)
        {
            if (ViewsContainer != null)
            {
                ViewsContainer.PushView(CreatePreviewWrapper(control), true, false);
            }
        }

#endif
    }
}